export default defineBackground(() => {
  console.log('🚀 Service Worker 启动 (最小版本)');

  // 只保留ping消息处理
  browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'ping') {
      console.log('🏓 ping响应');
      sendResponse({ success: true, message: 'pong' });
      return true;
    }
    
    // 其他消息暂时忽略
    console.log('📨 收到消息:', request.type);
    sendResponse({ success: false, error: '功能暂时禁用' });
    return true;
  });
});
