// Service Worker永久激活状态管理
let serviceWorkerConnections: Set<any> = new Set();
let keepAlivePort: any = null;

/**
 * 立即激活Service Worker
 */
async function performImmediateActivation() {
  console.log('🚀 执行Service Worker立即激活序列');
  
  try {
    // 执行一系列Chrome API调用来强制激活Service Worker
    await Promise.all([
      browser.storage.local.set({ _immediateActivation: Date.now() }),
      browser.storage.local.set({ _startupFlag: true }),
      browser.runtime.getPlatformInfo(),
      browser.tabs.query({ active: true }),
      browser.storage.local.get(['activationCounter'])
    ]);
    
    // 更新激活计数器
    const result = await browser.storage.local.get(['activationCounter']);
    const counter = (result.activationCounter || 0) + 1;
    await browser.storage.local.set({ activationCounter: counter });
    
    console.log('✅ Service Worker立即激活完成，激活次数:', counter);
    
    // 立即启动保活机制
    startPermanentServiceWorkerKeepAlive();
    
  } catch (error) {
    console.error('❌ Service Worker立即激活失败:', error);
    // 即使失败也要启动保活机制
    startPermanentServiceWorkerKeepAlive();
  }
}

/**
 * 启动Service Worker永久保活机制
 */
function startPermanentServiceWorkerKeepAlive() {
  console.log('🔧 启动Service Worker永久保活机制');
  
  // 创建一个与popup的长期连接
  browser.runtime.onConnect.addListener((port) => {
    console.log('🔗 收到新连接:', port.name);
    
    if (port.name === 'keep-alive') {
      keepAlivePort = port;
      serviceWorkerConnections.add(port);
      
      port.onDisconnect.addListener(() => {
        console.log('🔌 连接断开');
        serviceWorkerConnections.delete(port);
        if (keepAlivePort === port) {
          keepAlivePort = null;
        }
        // 立即尝试重新建立连接
        setTimeout(createKeepAliveConnection, 1000);
      });
      
      port.onMessage.addListener((msg) => {
        if (msg.type === 'ping') {
          port.postMessage({ type: 'pong', timestamp: Date.now() });
        }
      });
    }
  });
  
  // 创建保活连接
  createKeepAliveConnection();
}

/**
 * 创建保活连接
 */
async function createKeepAliveConnection() {
  try {
    // 创建一个隐藏的tab来保持连接
    const tabs = await browser.tabs.query({ active: true, currentWindow: true });
    
    if (tabs.length > 0) {
      const tabId = tabs[0].id;
      
      // 在当前tab注入内容脚本建立连接
      try {
        await browser.scripting.executeScript({
          target: { tabId },
          files: ['entrypoints/content.js']
        });
        
        // 发送消息给内容脚本建立连接
        browser.tabs.sendMessage(tabId, { 
          type: 'establish-keep-alive',
          timestamp: Date.now() 
        }).catch(() => {
          // 如果失败，尝试其他方法
          setupAlternativeKeepAlive();
        });
      } catch (error) {
        console.log('内容脚本注入失败，使用备用保活方法');
        setupAlternativeKeepAlive();
      }
    } else {
      setupAlternativeKeepAlive();
    }
  } catch (error) {
    console.log('保活连接创建失败，使用备用方法');
    setupAlternativeKeepAlive();
  }
}

/**
 * 备用保活方法
 */
function setupAlternativeKeepAlive() {
  console.log('🔧 使用备用Service Worker保活方法');
  
  // 方法1：持续的Chrome API调用
  let apiCallInterval: NodeJS.Timeout | null = setInterval(() => {
    Promise.all([
      browser.storage.local.set({ _keepAlive: Date.now() }),
      browser.runtime.getPlatformInfo(),
      browser.tabs.query({ active: true })
    ]).then(() => {
      console.log('💓 API调用保活:', Date.now());
    }).catch(error => {
      console.error('API调用保活失败:', error);
    });
  }, 8000); // 每8秒一次
  
  // 方法2：频繁的定时器
  let timerCounter = 0;
  let timerInterval: NodeJS.Timeout | null = setInterval(() => {
    timerCounter++;
    console.log(`🕐 定时器保活 #${timerCounter}:`, Date.now());
    
    // 每10次执行一次重操作
    if (timerCounter % 10 === 0) {
      forceWakeUpServiceWorker();
    }
  }, 5000); // 每5秒一次
  
  // 方法3：创建多个alarm
  browser.alarms.clearAll();
  browser.alarms.create('keep-alive-1', { 
    delayInMinutes: 0.5, 
    periodInMinutes: 0.5 
  });
  browser.alarms.create('keep-alive-2', { 
    delayInMinutes: 1, 
    periodInMinutes: 1 
  });
  browser.alarms.create('keep-alive-3', { 
    delayInMinutes: 2, 
    periodInMinutes: 2 
  });
  
  browser.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name.startsWith('keep-alive')) {
      console.log('⏰ Alarm保活:', alarm.name, Date.now());
      browser.storage.local.set({ 
        [`_${alarm.name}_timestamp`]: Date.now() 
      });
    }
  });
  
  // 清理函数
  browser.runtime.onSuspend.addListener(() => {
    console.log('🛑 清理保活定时器');
    if (apiCallInterval) clearInterval(apiCallInterval);
    if (timerInterval) clearInterval(timerInterval);
  });
}

export default defineBackground(() => {
  console.log('🚀 fwyy-tools background script started', { id: browser.runtime.id });

  // 立即执行激活操作，确保Service Worker完全启动
  performImmediateActivation();

  // 立即设置消息监听器，确保能收到ping请求
  browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 Background收到消息:', request);
    console.log('📍 消息来源:', sender);

    // 处理ping消息（用于唤醒service worker）
    if (request.type === 'ping') {
      console.log('🏓 收到ping消息，service worker已激活');
      sendResponse({ success: true, message: 'pong' });
      return true;
    }

    // 处理API迁移验证工具的消息
    if (request.type === 'api-migration') {
      console.log('🎯 处理API迁移消息...');
      // 异步处理消息，确保正确调用sendResponse
      handleApiMigrationMessage(request, sender, sendResponse).catch(error => {
        console.error('处理API迁移消息异常:', error);
        sendResponse({ success: false, error: error.message });
      });
      return true; // 保持消息通道开放
    }

    // 处理XUID工具的消息
    switch (request.action) {
      case 'getCurrentXuid':
        handleGetCurrentXuid(request, sender, sendResponse);
        return true; // 保持消息通道开放

      case 'setXuid':
        handleSetXuid(request, sender, sendResponse);
        return true;

      default:
        console.log('未知消息类型:', request.action);
    }
  });

  // XUID工具相关的后台脚本功能
  console.log('📦 初始化XUID后台功能...');
  initializeXuidBackground();

  // API迁移验证工具的后台脚本功能
  console.log('📦 初始化API迁移后台功能...');
  initializeApiMigrationBackground().then(() => {
    console.log('✅ API迁移后台功能初始化完成');
  }).catch(error => {
    console.error('❌ API迁移后台功能初始化失败:', error);
  });

  console.log('✅ 所有后台功能初始化完成');

  // 监听扩展安装和启动事件
  browser.runtime.onInstalled.addListener((details) => {
    console.log('🔧 扩展已安装/更新:', details.reason);
    // 强制唤醒Service Worker
    forceWakeUpServiceWorker();
  });

  browser.runtime.onStartup.addListener(() => {
    console.log('🔄 扩展已启动');
    // 强制唤醒Service Worker
    forceWakeUpServiceWorker();
  });
});

/**
 * 强制保持Service Worker活跃的多种机制
 */
function forceKeepServiceWorkerAlive() {
  console.log('🔧 启动强制Service Worker保活机制');

  // 机制1：频繁的存储操作（最有效）
  let storageInterval: NodeJS.Timeout | null = setInterval(() => {
    const timestamp = Date.now();
    browser.storage.local.set({ 
      _serviceWorkerHeartbeat: timestamp,
      lastKeepAlive: timestamp 
    }).then(() => {
      console.log('💓 Service Worker心跳:', timestamp);
    }).catch(error => {
      console.error('❌ Service Worker心跳失败:', error);
    });
  }, 15000); // 每15秒一次，比Chrome的30秒超时更频繁

  // 机制2：定时器本身也能保持Service Worker活跃
  let timerInterval: NodeJS.Timeout | null = setInterval(() => {
    console.log('🕐 Service Worker定时器保活:', Date.now());
  }, 10000); // 每10秒一次

  // 机制3：监听存储操作事件，用于响应外部唤醒请求
  browser.storage.onChanged.addListener((changes, areaName) => {
    if (areaName === 'local') {
      if (changes._serviceWorkerWakeTrigger) {
        console.log('🔧 收到Service Worker唤醒请求');
        forceWakeUpServiceWorker();
      }
      if (changes._serviceWorkerHeartbeat) {
        console.log('💓 检测到Service Worker心跳');
      }
    }
  });

  // 机制4：监听扩展事件，这些事件也会触发Service Worker
  browser.tabs.onCreated.addListener(() => {
    console.log('📑 新标签页创建，触发Service Worker活动');
  });

  browser.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete') {
      console.log('📑 标签页加载完成，触发Service Worker活动');
    }
  });

  // 机制5：创建一个长期存在的alarm
  browser.alarms.create('serviceWorkerKeepAlive', { 
    delayInMinutes: 1, 
    periodInMinutes: 1 
  });
  
  browser.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'serviceWorkerKeepAlive') {
      console.log('⏰ Service Worker Alarm保活:', Date.now());
      browser.storage.local.set({ _alarmHeartbeat: Date.now() });
    }
  });

  // 监听扩展卸载事件清理所有定时器
  browser.runtime.onSuspend.addListener(() => {
    console.log('🛑 Service Worker即将挂起，清理所有定时器');
    if (storageInterval) {
      clearInterval(storageInterval);
      storageInterval = null;
    }
    if (timerInterval) {
      clearInterval(timerInterval);
      timerInterval = null;
    }
  });
}

/**
 * 强制唤醒Service Worker
 */
function forceWakeUpServiceWorker() {
  console.log('🚀 强制唤醒Service Worker');
  
  // 立即执行一系列操作来强制激活Service Worker
  Promise.all([
    browser.storage.local.set({ _forceWakeUp: Date.now() }),
    browser.storage.local.get(['wakeUpCounter']),
    browser.runtime.getPlatformInfo(),
    browser.runtime.getContexts()
  ]).then(() => {
    console.log('✅ Service Worker强制唤醒完成');
    
    // 更新唤醒计数器
    browser.storage.local.get(['wakeUpCounter']).then(result => {
      const counter = (result.wakeUpCounter || 0) + 1;
      browser.storage.local.set({ wakeUpCounter: counter });
    });
  }).catch(error => {
    console.error('❌ Service Worker强制唤醒失败:', error);
  });
}

/**
 * 初始化XUID工具的后台脚本功能
 */
function initializeXuidBackground() {
  // 扩展安装时的初始化
  browser.runtime.onInstalled.addListener((details) => {
    console.log('fwyy-tools已安装', details);

    // 初始化XUID存储数据
    initializeXuidStorage();
  });

  // 扩展启动时的初始化
  browser.runtime.onStartup.addListener(() => {
    console.log('fwyy-tools已启动');
  });

  // 监听标签页更新事件
  browser.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 当页面完全加载后，可以进行一些初始化操作
    if (changeInfo.status === 'complete' && tab.url) {
      console.log('页面加载完成:', tab.url);
    }
  });

  // 监听Cookie变化事件
  browser.cookies.onChanged.addListener((changeInfo) => {
    // 只关注XUID Cookie的变化
    if (changeInfo.cookie.name === 'XUID') {
      console.log('XUID Cookie发生变化:', changeInfo);

      // 通知相关标签页更新显示
      notifyXuidUpdate(changeInfo);
    }
  });

  // 定期清理数据（每24小时）
  browser.alarms.create('xuid-cleanup', { periodInMinutes: 24 * 60 });
  browser.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'xuid-cleanup') {
      cleanupXuidData();
    }
  });
}

/**
 * 初始化XUID存储数据
 */
async function initializeXuidStorage() {
  try {
    const result = await browser.storage.local.get(['xuids', 'settings', 'assetInfo', 'assetInfoList']);

    // 如果没有数据，初始化默认数据
    if (!result.xuids) {
      await browser.storage.local.set({
        xuids: {},
        settings: {
          maxXuidCount: 100,
          expandedDomains: [],
          xuidCookieNames: ['XUID', 'xuid', 'Xuid', 'XUid', 'xuId', 'xUid', 'xUId']
        },
        assetInfo: {},
        assetInfoList: {}
      });
      console.log('XUID存储数据已初始化');
    }
  } catch (error) {
    console.error('初始化XUID存储失败:', error);
  }
}

/**
 * 处理获取当前XUID的请求
 */
async function handleGetCurrentXuid(request: any, sender: any, sendResponse: any) {
  try {
    if (!sender.tab) {
      sendResponse({ success: false, error: '无法获取标签页信息' });
      return;
    }

    const cookie = await browser.cookies.get({
      url: sender.tab.url,
      name: 'XUID'
    });

    sendResponse({
      success: true,
      xuid: cookie ? cookie.value : null,
      domain: new URL(sender.tab.url).hostname
    });

  } catch (error) {
    console.error('获取XUID失败:', error);
    sendResponse({ success: false, error: (error as Error).message });
  }
}

/**
 * 处理设置XUID的请求
 */
async function handleSetXuid(request: any, sender: any, sendResponse: any) {
  try {
    if (!sender.tab) {
      sendResponse({ success: false, error: '无法获取标签页信息' });
      return;
    }

    const url = new URL(sender.tab.url);

    await browser.cookies.set({
      url: sender.tab.url,
      name: 'XUID',
      value: request.xuid,
      domain: url.hostname,
      path: '/'
    });

    sendResponse({ success: true });

  } catch (error) {
    console.error('设置XUID失败:', error);
    sendResponse({ success: false, error: (error as Error).message });
  }
}

/**
 * 通知XUID更新
 */
async function notifyXuidUpdate(changeInfo: any) {
  try {
    // 获取所有标签页
    const tabs = await browser.tabs.query({});

    // 向每个标签页发送更新消息
    tabs.forEach(tab => {
      if (tab.url && tab.url.includes(changeInfo.cookie.domain) && tab.id) {
        browser.tabs.sendMessage(tab.id, {
          action: 'xuidChanged',
          xuid: changeInfo.cookie.value,
          domain: changeInfo.cookie.domain
        }).catch(() => {
          // 忽略无法发送消息的标签页（如chrome://页面）
        });
      }
    });

  } catch (error) {
    console.error('通知XUID更新失败:', error);
  }
}

/**
 * 清理过期的XUID数据
 */
async function cleanupXuidData() {
  try {
    const result = await browser.storage.local.get(['xuids']);

    if (result.xuids) {
      // 这里可以添加清理逻辑，比如删除长时间未使用的XUID
      console.log('XUID数据清理完成');
    }

  } catch (error) {
    console.error('XUID数据清理失败:', error);
  }
}

// API迁移验证工具相关变量和函数
let apiMigrationRules: any[] = [];
let isApiMigrationIntercepting = false;

/**
 * 初始化API迁移验证工具的后台脚本功能
 */
async function initializeApiMigrationBackground() {
  console.log('🔧 API迁移验证工具后台功能已初始化');

  // 消息监听器已在主函数中统一处理，这里不再重复添加

  // 初始化时加载规则
  console.log('📋 开始加载API迁移规则...');
  await loadApiMigrationRules();
  console.log('✅ API迁移规则加载完成');
}

/**
 * 处理API迁移验证工具的消息
 */
async function handleApiMigrationMessage(request: any, sender: any, sendResponse: any) {
  console.log('📨 background收到API迁移消息:', request);
  console.log('  发送者:', sender);

  try {
    let result;
    switch (request.action) {
      case 'start-interceptor':
        await startApiMigrationInterceptor();
        result = { success: true };
        break;

      case 'stop-interceptor':
        await stopApiMigrationInterceptor();
        result = { success: true };
        break;

      case 'get-status':
        console.log('📊 返回拦截器状态:', isApiMigrationIntercepting);
        result = {
          success: true,
          isIntercepting: isApiMigrationIntercepting
        };
        break;

      case 'update-rules':
        apiMigrationRules = request.rules;
        await saveApiMigrationRules();
        result = { success: true };
        break;

      case 'get-rules':
        console.log('📋 返回规则数据:', apiMigrationRules.length, '条规则');
        result = {
          success: true,
          rules: apiMigrationRules
        };
        break;

      default:
        result = { success: false, error: '未知动作' };
    }
    
    // 确保异步操作完成后发送响应
    sendResponse(result);
  } catch (error) {
    console.error('处理API迁移消息失败:', error);
    sendResponse({ success: false, error: (error as Error).message });
  }
}

/**
 * 启动API迁移拦截器
 */
async function startApiMigrationInterceptor() {
  if (isApiMigrationIntercepting) {
    console.log('API迁移拦截器已在运行');
    return;
  }

  try {
    console.log('🔄 启动API迁移拦截器');
    
    // 注册webRequest监听器
    browser.webRequest.onBeforeRequest.addListener(
      handleApiRequest,
      { urls: ['<all_urls>'] },
      ['requestBody']
    );

    // 注册webRequest监听器用于修改请求头
    browser.webRequest.onBeforeSendHeaders.addListener(
      handleApiRequestHeaders,
      { urls: ['<all_urls>'] },
      ['requestHeaders', 'blocking']
    );

    isApiMigrationIntercepting = true;
    console.log('✅ API迁移拦截器已启动');

  } catch (error) {
    console.error('启动API迁移拦截器失败:', error);
    throw error;
  }
}

/**
 * 停止API迁移拦截器
 */
async function stopApiMigrationInterceptor() {
  if (!isApiMigrationIntercepting) {
    console.log('API迁移拦截器未在运行');
    return;
  }

  try {
    console.log('⏹️ 停止API迁移拦截器');

    // 移除webRequest监听器
    browser.webRequest.onBeforeRequest.removeListener(handleApiRequest);
    browser.webRequest.onBeforeSendHeaders.removeListener(handleApiRequestHeaders);

    isApiMigrationIntercepting = false;
    console.log('✅ API迁移拦截器已停止');

  } catch (error) {
    console.error('停止API迁移拦截器失败:', error);
    throw error;
  }
}

/**
 * 处理API请求
 */
function handleApiRequest(details: any) {
  // 只处理主框架的请求，避免处理iframe等子资源
  if (details.type !== 'main_frame' && details.type !== 'xmlhttprequest' && details.type !== 'fetch') {
    return;
  }

  console.log('🔍 拦截到API请求:', details.url);

  // 查找匹配的规则
  const matchedRule = findMatchingApiRule(details.url, details.method);
  if (!matchedRule) {
    console.log('❌ 未找到匹配规则');
    return;
  }

  console.log('✅ 找到匹配规则:', matchedRule.name);

  try {
    if (matchedRule.mode === 'redirect') {
      // 重定向模式
      const newUrl = transformApiUrl(details.url, matchedRule);
      console.log('🔄 重定向请求:', { original: details.url, new: newUrl });
      
      return {
        redirectUrl: newUrl
      };
    } else {
      // 并行对比模式 - 异步执行，不阻塞请求
      executeParallelComparison(details, matchedRule).catch(error => {
        console.error('并行对比失败:', error);
      });
      return; // 不修改原请求
    }
  } catch (error) {
    console.error('处理API请求失败:', error);
  }
}

/**
 * 处理API请求头
 */
function handleApiRequestHeaders(details: any) {
  // 这里可以添加请求头的修改逻辑
  // 目前先返回，不修改请求头
  return { requestHeaders: details.requestHeaders };
}

/**
 * 查找匹配的API规则
 */
function findMatchingApiRule(url: string, method: string): any {
  // 按优先级排序过滤启用的规则
  const enabledRules = apiMigrationRules
    .filter(rule => rule.enabled)
    .sort((a, b) => b.priority - a.priority);

  for (const rule of enabledRules) {
    if (isApiRuleMatched(rule, url, method)) {
      return rule;
    }
  }
  return null;
}

/**
 * 检查规则是否匹配
 */
function isApiRuleMatched(rule: any, url: string, method: string): boolean {
  const { conditions } = rule;

  // URL匹配
  if (!isApiUrlMatched(conditions.urlPattern, conditions.urlMatchType, url)) {
    return false;
  }

  // HTTP方法匹配
  if (conditions.methods && conditions.methods.length > 0) {
    if (!conditions.methods.includes(method.toUpperCase())) {
      return false;
    }
  }

  return true;
}

/**
 * URL匹配检查
 */
function isApiUrlMatched(pattern: string, matchType: string, url: string): boolean {
  switch (matchType) {
    case 'exact':
      return url === pattern;
    case 'contains':
      return url.includes(pattern);
    case 'startsWith':
      return url.startsWith(pattern);
    case 'endsWith':
      return url.endsWith(pattern);
    case 'regex':
      try {
        return new RegExp(pattern).test(url);
      } catch {
        return false;
      }
    default:
      return url.includes(pattern);
  }
}

/**
 * 转换API URL
 */
function transformApiUrl(originalUrl: string, rule: any): string {
  const { transformation } = rule;
  let newUrl = transformation.newUrl;

  // 参数映射逻辑
  if (transformation.paramMapping) {
    try {
      const urlObj = new URL(originalUrl);
      const newUrlObj = new URL(newUrl);

      for (const [oldKey, newKey] of Object.entries(transformation.paramMapping)) {
        const value = urlObj.searchParams.get(oldKey);
        if (value && newKey) {
          newUrlObj.searchParams.set(newKey as string, value);
        }
      }

      if (transformation.preserveOriginalParams) {
        urlObj.searchParams.forEach((value, key) => {
          if (!transformation.paramMapping[key] && !newUrlObj.searchParams.has(key)) {
            newUrlObj.searchParams.set(key, value);
          }
        });
      }

      newUrl = newUrlObj.toString();
    } catch (error) {
      console.warn('URL转换失败:', error);
    }
  }

  return newUrl;
}

/**
 * 执行并行对比
 */
async function executeParallelComparison(details: any, rule: any) {
  try {
    console.log('🔄 开始并行对比:', details.url);

    const transformedUrl = transformApiUrl(details.url, rule);
    
    // 并行调用新老接口
    const [oldResponse, newResponse] = await Promise.all([
      makeApiRequest(details.url, details.method, details.requestHeaders),
      makeApiRequest(transformedUrl, details.method, details.requestHeaders)
    ]);

    // 生成对比报告
    const report = await generateApiDiffReport(details, rule, oldResponse, newResponse);
    
    // 保存报告
    await saveApiDiffReport(report);

    // 通知popup更新
    browser.runtime.sendMessage({
      type: 'api-migration',
      action: 'new-report',
      report: report
    }).catch(() => {
      // popup可能已关闭，忽略错误
    });

    console.log('✅ 并行对比完成，发现', report.diff.changeCount, '处差异');

  } catch (error) {
    console.error('并行对比失败:', error);
  }
}

/**
 * 发起API请求
 */
async function makeApiRequest(url: string, method: string, headers?: any[]): Promise<any> {
  const startTime = Date.now();

  try {
    const headersObj: Record<string, string> = {};
    if (headers) {
      headers.forEach(header => {
        headersObj[header.name] = header.value;
      });
    }

    const response = await fetch(url, {
      method: method,
      headers: headersObj,
      credentials: 'include'
    });

    const responseTime = Date.now() - startTime;
    const body = await response.text(); // 先获取文本，避免JSON解析错误

    return {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      body: body,
      responseTime: responseTime
    };
  } catch (error) {
    return {
      status: 0,
      statusText: 'Network Error',
      headers: {},
      body: null,
      responseTime: Date.now() - startTime,
      error: (error as Error).message
    };
  }
}

/**
 * 生成API差异报告
 */
async function generateApiDiffReport(details: any, rule: any, oldResponse: any, newResponse: any): Promise<any> {
  // 简化的差异计算（可以后续扩展为更复杂的差异算法）
  const hasChanges = oldResponse.status !== newResponse.status || 
                     oldResponse.body !== newResponse.body;
  
  const changeCount = hasChanges ? 1 : 0;
  const severity = oldResponse.status !== newResponse.status ? 'critical' : 
                    (hasChanges ? 'minor' : 'none');

  return {
    id: 'report_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
    ruleId: rule.id,
    ruleName: rule.name,
    timestamp: Date.now(),
    request: {
      url: details.url,
      method: details.method
    },
    responses: {
      old: {
        status: oldResponse.status,
        body: oldResponse.body,
        responseTime: oldResponse.responseTime
      },
      new: {
        status: newResponse.status,
        body: newResponse.body,
        responseTime: newResponse.responseTime
      }
    },
    diff: {
      hasChanges,
      changeCount,
      severity
    },
    visualizations: {
      html: generateSimpleDiffHtml(oldResponse, newResponse),
      summary: generateSimpleDiffSummary(oldResponse, newResponse)
    }
  };
}

/**
 * 生成简单差异HTML
 */
function generateSimpleDiffHtml(oldResponse: any, newResponse: any): string {
  const hasChanges = oldResponse.status !== newResponse.status || 
                     oldResponse.body !== newResponse.body;
  
  if (!hasChanges) {
    return '<div class="alert alert-success">响应完全一致</div>';
  }

  let html = '<div class="diff-container">';
  
  if (oldResponse.status !== newResponse.status) {
    html += `<div class="diff-item status-diff">
      <span class="diff-label">状态码:</span>
      <span class="diff-old">${oldResponse.status}</span>
      <span class="diff-arrow">→</span>
      <span class="diff-new">${newResponse.status}</span>
    </div>`;
  }

  if (oldResponse.body !== newResponse.body) {
    html += `<div class="diff-item body-diff">
      <span class="diff-label">响应体:</span>
      <span class="diff-changed">内容不同</span>
    </div>`;
  }

  html += '</div>';
  return html;
}

/**
 * 生成简单差异摘要
 */
function generateSimpleDiffSummary(oldResponse: any, newResponse: any): string {
  const changes = [];
  
  if (oldResponse.status !== newResponse.status) {
    changes.push(`状态码: ${oldResponse.status} → ${newResponse.status}`);
  }
  
  if (oldResponse.body !== newResponse.body) {
    changes.push('响应体内容不同');
  }

  return changes.length > 0 ? changes.join(', ') : '无差异';
}

/**
 * 保存API差异报告
 */
async function saveApiDiffReport(report: any) {
  try {
    const result = await browser.storage.local.get(['apiMigrationReports']);
    const reports = result.apiMigrationReports || [];
    
    reports.unshift(report);
    
    // 限制报告数量
    const maxReports = 100;
    if (reports.length > maxReports) {
      reports.length = maxReports;
    }
    
    await browser.storage.local.set({ apiMigrationReports: reports });
    console.log('📊 API差异报告已保存');
  } catch (error) {
    console.error('保存API差异报告失败:', error);
  }
}

/**
 * 加载API迁移规则
 */
async function loadApiMigrationRules() {
  try {
    const result = await browser.storage.local.get(['apiMigrationRules']);
    apiMigrationRules = result.apiMigrationRules || [];
    console.log('📋 已加载', apiMigrationRules.length, '条API迁移规则');
  } catch (error) {
    console.error('加载API迁移规则失败:', error);
    apiMigrationRules = [];
  }
}

/**
 * 保存API迁移规则
 */
async function saveApiMigrationRules() {
  try {
    await browser.storage.local.set({ apiMigrationRules });
    console.log('💾 API迁移规则已保存');
  } catch (error) {
    console.error('保存API迁移规则失败:', error);
  }
}
