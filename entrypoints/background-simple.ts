export default defineBackground(() => {
  console.log('🚀 Service Worker 启动 (极简版本)');

  // 消息监听器
  browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 收到消息:', request.type);

    // 处理ping消息
    if (request.type === 'ping') {
      console.log('🏓 ping响应');
      sendResponse({ success: true, message: 'pong' });
      return true;
    }

    // 处理API迁移验证工具的消息
    if (request.type === 'api-migration') {
      console.log('🎯 处理API迁移消息');
      handleApiMigrationMessage(request, sender, sendResponse).catch(error => {
        console.error('处理API迁移消息异常:', error);
        sendResponse({ success: false, error: error.message });
      });
      return true;
    }

    // 处理XUID工具的消息
    switch (request.action) {
      case 'getCurrentXuid':
        handleGetCurrentXuid(request, sender, sendResponse);
        return true;

      case 'setXuid':
        handleSetXuid(request, sender, sendResponse);
        return true;

      case 'clearXuid':
        handleClearXuid(request, sender, sendResponse);
        return true;

      case 'getXuidHistory':
        handleGetXuidHistory(request, sender, sendResponse);
        return true;

      case 'clearXuidHistory':
        handleClearXuidHistory(request, sender, sendResponse);
        return true;

      case 'exportXuidHistory':
        handleExportXuidHistory(request, sender, sendResponse);
        return true;

      case 'importXuidHistory':
        handleImportXuidHistory(request, sender, sendResponse);
        return true;

      case 'getXuidSettings':
        handleGetXuidSettings(request, sender, sendResponse);
        return true;

      case 'updateXuidSettings':
        handleUpdateXuidSettings(request, sender, sendResponse);
        return true;

      default:
        console.log('未知消息类型:', request);
        sendResponse({ success: false, error: '未知消息类型' });
        return false;
    }
  });
});

// API迁移验证工具消息处理
async function handleApiMigrationMessage(request: any, sender: any, sendResponse: any) {
  const { action } = request;

  switch (action) {
    case 'start-intercept':
      await startApiIntercept(request.config);
      sendResponse({ success: true, message: 'API拦截已启动' });
      break;

    case 'stop-intercept':
      await stopApiIntercept();
      sendResponse({ success: true, message: 'API拦截已停止' });
      break;

    case 'get-status':
      const status = await getInterceptStatus();
      sendResponse({ success: true, ...status });
      break;

    case 'get-reports':
      const reports = await getApiReports();
      sendResponse({ success: true, reports });
      break;

    case 'clear-reports':
      await clearApiReports();
      sendResponse({ success: true, message: '报告已清空' });
      break;

    default:
      sendResponse({ success: false, error: '未知API迁移操作' });
  }
}

// XUID工具消息处理函数
async function handleGetCurrentXuid(request: any, sender: any, sendResponse: any) {
  try {
    const tabs = await browser.tabs.query({ active: true, currentWindow: true });
    if (tabs.length === 0) {
      sendResponse({ success: false, error: '未找到活动标签页' });
      return;
    }

    const tab = tabs[0];
    if (!tab.url || !tab.url.includes('xbox.com')) {
      sendResponse({ success: false, error: '当前页面不是Xbox网站' });
      return;
    }

    const results = await browser.scripting.executeScript({
      target: { tabId: tab.id! },
      func: () => {
        const xuidElement = document.querySelector('[data-xuid]');
        return xuidElement ? xuidElement.getAttribute('data-xuid') : null;
      }
    });

    const xuid = results[0]?.result;
    if (xuid) {
      sendResponse({ success: true, xuid });
    } else {
      sendResponse({ success: false, error: '未找到XUID' });
    }
  } catch (error) {
    console.error('获取XUID失败:', error);
    sendResponse({ success: false, error: '获取XUID失败' });
  }
}

async function handleSetXuid(request: any, sender: any, sendResponse: any) {
  try {
    const { xuid } = request;
    await browser.storage.local.set({ currentXuid: xuid });
    sendResponse({ success: true, message: 'XUID已设置' });
  } catch (error) {
    console.error('设置XUID失败:', error);
    sendResponse({ success: false, error: '设置XUID失败' });
  }
}

async function handleClearXuid(request: any, sender: any, sendResponse: any) {
  try {
    await browser.storage.local.remove('currentXuid');
    sendResponse({ success: true, message: 'XUID已清除' });
  } catch (error) {
    console.error('清除XUID失败:', error);
    sendResponse({ success: false, error: '清除XUID失败' });
  }
}

async function handleGetXuidHistory(request: any, sender: any, sendResponse: any) {
  try {
    const result = await browser.storage.local.get('xuidHistory');
    const history = result.xuidHistory || [];
    sendResponse({ success: true, history });
  } catch (error) {
    console.error('获取XUID历史失败:', error);
    sendResponse({ success: false, error: '获取XUID历史失败' });
  }
}

async function handleClearXuidHistory(request: any, sender: any, sendResponse: any) {
  try {
    await browser.storage.local.remove('xuidHistory');
    sendResponse({ success: true, message: 'XUID历史已清除' });
  } catch (error) {
    console.error('清除XUID历史失败:', error);
    sendResponse({ success: false, error: '清除XUID历史失败' });
  }
}

async function handleExportXuidHistory(request: any, sender: any, sendResponse: any) {
  try {
    const result = await browser.storage.local.get('xuidHistory');
    const history = result.xuidHistory || [];
    sendResponse({ success: true, data: JSON.stringify(history, null, 2) });
  } catch (error) {
    console.error('导出XUID历史失败:', error);
    sendResponse({ success: false, error: '导出XUID历史失败' });
  }
}

async function handleImportXuidHistory(request: any, sender: any, sendResponse: any) {
  try {
    const { data } = request;
    const history = JSON.parse(data);
    await browser.storage.local.set({ xuidHistory: history });
    sendResponse({ success: true, message: 'XUID历史已导入' });
  } catch (error) {
    console.error('导入XUID历史失败:', error);
    sendResponse({ success: false, error: '导入XUID历史失败' });
  }
}

async function handleGetXuidSettings(request: any, sender: any, sendResponse: any) {
  try {
    const result = await browser.storage.local.get('xuidSettings');
    const settings = result.xuidSettings || {};
    sendResponse({ success: true, settings });
  } catch (error) {
    console.error('获取XUID设置失败:', error);
    sendResponse({ success: false, error: '获取XUID设置失败' });
  }
}

async function handleUpdateXuidSettings(request: any, sender: any, sendResponse: any) {
  try {
    const { settings } = request;
    await browser.storage.local.set({ xuidSettings: settings });
    sendResponse({ success: true, message: 'XUID设置已更新' });
  } catch (error) {
    console.error('更新XUID设置失败:', error);
    sendResponse({ success: false, error: '更新XUID设置失败' });
  }
}

// API拦截相关函数（简化版本）
async function startApiIntercept(config: any) {
  console.log('启动API拦截:', config);
  // 简化实现
}

async function stopApiIntercept() {
  console.log('停止API拦截');
  // 简化实现
}

async function getInterceptStatus() {
  return { isIntercepting: false };
}

async function getApiReports() {
  return [];
}

async function clearApiReports() {
  console.log('清空API报告');
}
