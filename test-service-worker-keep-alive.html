<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Worker 保活测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .status-card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
        }
        .status-card h3 {
            margin-top: 0;
            color: #333;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-active { background-color: #28a745; }
        .status-inactive { background-color: #dc3545; }
        .status-unknown { background-color: #ffc107; }
        .log-section {
            margin-top: 20px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        .alert {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Service Worker 保活测试工具</h1>
        
        <div class="status-section">
            <div class="status-card">
                <h3>Service Worker 状态</h3>
                <div id="sw-status">
                    <span class="status-indicator status-unknown"></span>
                    <span id="sw-status-text">检查中...</span>
                </div>
                <div style="margin-top: 10px;">
                    <small>最后活动: <span id="sw-last-activity">未知</span></small>
                </div>
            </div>
            
            <div class="status-card">
                <h3>连接状态</h3>
                <div id="connection-status">
                    <span class="status-indicator status-unknown"></span>
                    <span id="connection-status-text">检查中...</span>
                </div>
                <div style="margin-top: 10px;">
                    <small>连接方式: <span id="connection-type">未知</span></small>
                </div>
            </div>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-value" id="ping-count">0</div>
                <div class="stat-label">Ping 成功次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="ping-failures">0</div>
                <div class="stat-label">Ping 失败次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avg-response-time">0ms</div>
                <div class="stat-label">平均响应时间</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="uptime-minutes">0</div>
                <div class="stat-label">运行时间 (分钟)</div>
            </div>
        </div>

        <div class="controls">
            <button id="check-status-btn">检查状态</button>
            <button id="test-ping-btn">测试 Ping</button>
            <button id="establish-connection-btn">建立保活连接</button>
            <button id="start-keep-alive-btn">开始自动保活</button>
            <button id="stop-keep-alive-btn">停止自动保活</button>
            <button id="clear-log-btn">清空日志</button>
        </div>

        <div id="alerts"></div>

        <div class="log-section">
            <h3>实时日志</h3>
            <div class="log" id="log"></div>
        </div>
    </div>

    <script>
        class ServiceWorkerKeepAliveTester {
            constructor() {
                this.pingCount = 0;
                this.pingFailures = 0;
                this.responseTimes = [];
                this.startTime = Date.now();
                this.keepAliveInterval = null;
                this.connectionPort = null;
                
                this.initializeElements();
                this.bindEvents();
                this.startStatusCheck();
            }

            initializeElements() {
                this.elements = {
                    swStatus: document.getElementById('sw-status'),
                    swStatusText: document.getElementById('sw-status-text'),
                    swLastActivity: document.getElementById('sw-last-activity'),
                    connectionStatus: document.getElementById('connection-status'),
                    connectionStatusText: document.getElementById('connection-status-text'),
                    connectionType: document.getElementById('connection-type'),
                    pingCount: document.getElementById('ping-count'),
                    pingFailures: document.getElementById('ping-failures'),
                    avgResponseTime: document.getElementById('avg-response-time'),
                    uptimeMinutes: document.getElementById('uptime-minutes'),
                    log: document.getElementById('log'),
                    alerts: document.getElementById('alerts')
                };
            }

            bindEvents() {
                document.getElementById('check-status-btn').addEventListener('click', () => this.checkServiceWorkerStatus());
                document.getElementById('test-ping-btn').addEventListener('click', () => this.testPing());
                document.getElementById('establish-connection-btn').addEventListener('click', () => this.establishKeepAliveConnection());
                document.getElementById('start-keep-alive-btn').addEventListener('click', () => this.startAutoKeepAlive());
                document.getElementById('stop-keep-alive-btn').addEventListener('click', () => this.stopAutoKeepAlive());
                document.getElementById('clear-log-btn').addEventListener('click', () => this.clearLog());
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const div = document.createElement('div');
                div.className = type;
                div.textContent = `[${timestamp}] ${message}`;
                this.elements.log.appendChild(div);
                this.elements.log.scrollTop = this.elements.log.scrollHeight;
            }

            showAlert(message, type = 'info') {
                const alert = document.createElement('div');
                alert.className = `alert alert-${type}`;
                alert.textContent = message;
                this.elements.alerts.appendChild(alert);
                
                // 5秒后自动移除
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 5000);
            }

            updateStatus() {
                const uptime = Math.floor((Date.now() - this.startTime) / 60000);
                this.elements.uptimeMinutes.textContent = uptime;
                
                const avgTime = this.responseTimes.length > 0 
                    ? Math.round(this.responseTimes.reduce((a, b) => a + b) / this.responseTimes.length)
                    : 0;
                this.elements.avgResponseTime.textContent = avgTime + 'ms';
                
                this.elements.pingCount.textContent = this.pingCount;
                this.elements.pingFailures.textContent = this.pingFailures;
            }

            async checkServiceWorkerStatus() {
                try {
                    this.log('检查Service Worker状态...', 'info');
                    
                    if (typeof chrome === 'undefined' && typeof browser === 'undefined') {
                        throw new Error('浏览器不支持扩展API');
                    }

                    const api = chrome || browser;
                    
                    // 检查扩展基本信息
                    const manifest = api.runtime.getManifest();
                    this.log(`扩展名称: ${manifest.name}`, 'info');
                    this.log(`扩展版本: ${manifest.version}`, 'info');
                    this.log(`Manifest版本: ${manifest.manifest_version}`, 'info');

                    // 测试ping
                    const startTime = Date.now();
                    const response = await Promise.race([
                        api.runtime.sendMessage({ type: 'ping' }),
                        new Promise((_, reject) => 
                            setTimeout(() => reject(new Error('Ping超时')), 3000)
                        )
                    ]);
                    
                    const responseTime = Date.now() - startTime;
                    this.responseTimes.push(responseTime);
                    
                    if (response && response.success) {
                        this.updateServiceWorkerStatus(true, `正常运行 (响应时间: ${responseTime}ms)`);
                        this.pingCount++;
                        this.showAlert('Service Worker 正常运行', 'success');
                        this.log(`✅ Ping成功，响应时间: ${responseTime}ms`, 'success');
                    } else {
                        throw new Error('响应格式无效');
                    }
                    
                } catch (error) {
                    this.pingFailures++;
                    this.updateServiceWorkerStatus(false, `无响应: ${error.message}`);
                    this.showAlert(`Service Worker 无响应: ${error.message}`, 'error');
                    this.log(`❌ Ping失败: ${error.message}`, 'error');
                }
                
                this.updateStatus();
            }

            updateServiceWorkerStatus(isActive, message) {
                const indicator = this.elements.swStatus.querySelector('.status-indicator');
                const text = this.elements.swStatusText;
                
                if (isActive) {
                    indicator.className = 'status-indicator status-active';
                    text.textContent = '活动';
                    this.elements.swLastActivity.textContent = new Date().toLocaleTimeString();
                } else {
                    indicator.className = 'status-indicator status-inactive';
                    text.textContent = '不活动';
                }
                
                this.log(`Service Worker状态更新: ${message}`, 'info');
            }

            updateConnectionStatus(isConnected, message, connectionType = '未知') {
                const indicator = this.elements.connectionStatus.querySelector('.status-indicator');
                const text = this.elements.connectionStatusText;
                
                if (isConnected) {
                    indicator.className = 'status-indicator status-active';
                    text.textContent = '已连接';
                } else {
                    indicator.className = 'status-indicator status-inactive';
                    text.textContent = '未连接';
                }
                
                this.elements.connectionType.textContent = connectionType;
                this.log(`连接状态更新: ${message}`, 'info');
            }

            establishKeepAliveConnection() {
                try {
                    this.log('尝试建立Service Worker保活连接...', 'info');
                    
                    if (typeof chrome === 'undefined' && typeof browser === 'undefined') {
                        throw new Error('浏览器不支持扩展API');
                    }

                    const api = chrome || browser;
                    
                    // 建立长期连接
                    this.connectionPort = api.runtime.connect({ name: 'keep-alive' });
                    
                    this.connectionPort.onDisconnect.addListener(() => {
                        this.log('❌ Service Worker连接断开', 'error');
                        this.updateConnectionStatus(false, '连接断开', '无连接');
                        this.showAlert('Service Worker 连接断开', 'warning');
                    });
                    
                    this.connectionPort.onMessage.addListener((msg) => {
                        if (msg.type === 'pong') {
                            this.log(`💓 收到心跳响应: ${msg.timestamp}`, 'success');
                            this.updateConnectionStatus(true, '心跳正常', '端口连接');
                        }
                    });
                    
                    // 发送测试消息
                    this.connectionPort.postMessage({ type: 'ping', timestamp: Date.now() });
                    
                    this.updateConnectionStatus(true, '保活连接已建立', '端口连接');
                    this.showAlert('Service Worker 保活连接建立成功', 'success');
                    this.log('✅ Service Worker保活连接建立成功', 'success');
                    
                } catch (error) {
                    this.updateConnectionStatus(false, `连接失败: ${error.message}`, '连接失败');
                    this.showAlert(`建立保活连接失败: ${error.message}`, 'error');
                    this.log(`❌ 建立保活连接失败: ${error.message}`, 'error');
                }
            }

            startAutoKeepAlive() {
                if (this.keepAliveInterval) {
                    this.showAlert('自动保活已在运行中', 'warning');
                    return;
                }
                
                this.log('启动自动保活机制...', 'info');
                
                // 每15秒检查一次Service Worker状态
                this.keepAliveInterval = setInterval(async () => {
                    try {
                        const api = chrome || browser;
                        const response = await Promise.race([
                            api.runtime.sendMessage({ type: 'ping' }),
                            new Promise((_, reject) => 
                                setTimeout(() => reject(new Error('自动保活Ping超时')), 2000)
                            )
                        ]);
                        
                        if (response && response.success) {
                            this.log('💓 自动保活Ping成功', 'success');
                        } else {
                            this.log('⚠️ 自动保活Ping响应无效', 'warning');
                        }
                    } catch (error) {
                        this.log(`⚠️ 自动保活Ping失败: ${error.message}`, 'warning');
                        // 尝试存储唤醒
                        try {
                            await api.storage.local.set({ _autoKeepAlive: Date.now() });
                            this.log('🔄 已执行存储唤醒', 'info');
                        } catch (storageError) {
                            this.log(`❌ 存储唤醒失败: ${storageError.message}`, 'error');
                        }
                    }
                }, 15000);
                
                this.showAlert('自动保活已启动 (每15秒检查一次)', 'success');
                this.log('✅ 自动保活机制已启动', 'success');
            }

            stopAutoKeepAlive() {
                if (this.keepAliveInterval) {
                    clearInterval(this.keepAliveInterval);
                    this.keepAliveInterval = null;
                    this.showAlert('自动保活已停止', 'info');
                    this.log('⏹️ 自动保活机制已停止', 'info');
                } else {
                    this.showAlert('自动保活未在运行', 'warning');
                }
            }

            clearLog() {
                this.elements.log.innerHTML = '';
                this.log('日志已清空', 'info');
            }

            async startStatusCheck() {
                // 初始检查
                await this.checkServiceWorkerStatus();
                
                // 每30秒自动检查状态
                setInterval(() => {
                    this.checkServiceWorkerStatus();
                }, 30000);
                
                // 每5秒更新统计信息
                setInterval(() => {
                    this.updateStatus();
                }, 5000);
            }
        }

        // 启动测试工具
        window.addEventListener('load', () => {
            new ServiceWorkerKeepAliveTester();
        });
    </script>
</body>
</html>