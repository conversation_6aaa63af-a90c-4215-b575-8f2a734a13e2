<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Worker 状态测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
        }
        .status.active {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.inactive {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.unknown {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .log-entry.success {
            background: #d4edda;
            color: #155724;
        }
        .log-entry.error {
            background: #f8d7da;
            color: #721c24;
        }
        .log-entry.info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Service Worker 状态监控</h1>
        
        <div id="status" class="status unknown">
            🔍 正在检测 Service Worker 状态...
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div id="pingCount" class="stat-number">0</div>
                <div class="stat-label">Ping 次数</div>
            </div>
            <div class="stat-card">
                <div id="successCount" class="stat-number">0</div>
                <div class="stat-label">成功次数</div>
            </div>
            <div class="stat-card">
                <div id="failCount" class="stat-number">0</div>
                <div class="stat-label">失败次数</div>
            </div>
            <div class="stat-card">
                <div id="avgResponseTime" class="stat-number">0ms</div>
                <div class="stat-label">平均响应时间</div>
            </div>
        </div>
        
        <div>
            <button onclick="testServiceWorker()">🏓 测试 Service Worker</button>
            <button onclick="startContinuousTest()">🔄 开始连续测试</button>
            <button onclick="stopContinuousTest()">⏹️ 停止测试</button>
            <button onclick="clearLog()">🗑️ 清空日志</button>
            <button onclick="testApiMigrationTool()">🔧 测试 API 迁移工具</button>
        </div>
        
        <div id="log" class="log">
            <div class="log-entry info">📋 Service Worker 状态监控已启动</div>
        </div>
    </div>

    <script>
        let pingCount = 0;
        let successCount = 0;
        let failCount = 0;
        let responseTimes = [];
        let continuousTestInterval = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStats() {
            document.getElementById('pingCount').textContent = pingCount;
            document.getElementById('successCount').textContent = successCount;
            document.getElementById('failCount').textContent = failCount;
            
            const avgTime = responseTimes.length > 0 
                ? Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length)
                : 0;
            document.getElementById('avgResponseTime').textContent = `${avgTime}ms`;
        }

        function updateStatus(isActive, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${isActive ? 'active' : 'inactive'}`;
            statusDiv.textContent = message;
        }

        async function testServiceWorker() {
            pingCount++;
            const startTime = Date.now();
            
            try {
                log('🏓 发送 ping 到 Service Worker...', 'info');
                
                const response = await chrome.runtime.sendMessage({ type: 'ping' });
                const responseTime = Date.now() - startTime;
                responseTimes.push(responseTime);
                
                if (response && response.success) {
                    successCount++;
                    updateStatus(true, `✅ Service Worker 活跃 (响应时间: ${responseTime}ms)`);
                    log(`✅ Service Worker 响应成功 (${responseTime}ms)`, 'success');
                } else {
                    failCount++;
                    updateStatus(false, '❌ Service Worker 响应异常');
                    log('❌ Service Worker 响应异常', 'error');
                }
            } catch (error) {
                failCount++;
                const responseTime = Date.now() - startTime;
                updateStatus(false, `❌ Service Worker 无响应 (${responseTime}ms)`);
                log(`❌ Service Worker 测试失败: ${error.message} (${responseTime}ms)`, 'error');
            }
            
            updateStats();
        }

        function startContinuousTest() {
            if (continuousTestInterval) {
                clearInterval(continuousTestInterval);
            }
            
            log('🔄 开始连续测试 (每5秒一次)', 'info');
            continuousTestInterval = setInterval(testServiceWorker, 5000);
        }

        function stopContinuousTest() {
            if (continuousTestInterval) {
                clearInterval(continuousTestInterval);
                continuousTestInterval = null;
                log('⏹️ 连续测试已停止', 'info');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = 
                '<div class="log-entry info">📋 日志已清空</div>';
        }

        async function testApiMigrationTool() {
            try {
                log('🔧 测试 API 迁移验证工具...', 'info');
                
                const response = await chrome.runtime.sendMessage({
                    type: 'api-migration',
                    action: 'get-status'
                });
                
                if (response && response.success) {
                    log(`✅ API 迁移工具状态: ${response.isIntercepting ? '运行中' : '已停止'}`, 'success');
                } else {
                    log('❌ API 迁移工具测试失败', 'error');
                }
            } catch (error) {
                log(`❌ API 迁移工具测试异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试一次
        window.addEventListener('load', () => {
            setTimeout(testServiceWorker, 1000);
        });
    </script>
</body>
</html>
