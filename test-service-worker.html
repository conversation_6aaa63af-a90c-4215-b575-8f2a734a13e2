<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Worker 测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.active {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.inactive {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.unknown {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Service Worker 测试工具</h1>
        
        <div id="status" class="status unknown">
            检查中...
        </div>
        
        <div>
            <button id="checkStatus">检查状态</button>
            <button id="pingTest">Ping 测试</button>
            <button id="forceWakeUp">强制唤醒</button>
            <button id="clearLog">清空日志</button>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `[${timestamp}] ${message}`;
            log.appendChild(div);
            log.scrollTop = log.scrollHeight;
        }
        
        function updateStatus(state, message) {
            status.className = `status ${state}`;
            status.textContent = message;
            addLog(`状态更新: ${message}`, state);
        }
        
        async function checkExtensionStatus() {
            try {
                addLog('检查扩展状态...', 'info');
                
                // 检查扩展是否可用
                if (typeof chrome === 'undefined' && typeof browser === 'undefined') {
                    throw new Error('浏览器不支持扩展API');
                }
                
                const api = chrome || browser;
                
                // 检查runtime
                const runtimeInfo = await api.runtime.getManifest();
                addLog(`扩展名称: ${runtimeInfo.name}`, 'info');
                addLog(`扩展版本: ${runtimeInfo.version}`, 'info');
                addLog(`Manifest版本: ${runtimeInfo.manifest_version}`, 'info');
                
                // 检查权限
                const permissions = runtimeInfo.permissions || [];
                addLog(`权限列表: ${permissions.join(', ')}`, 'info');
                
                updateStatus('active', '扩展运行正常');
                
            } catch (error) {
                addLog(`扩展状态检查失败: ${error.message}`, 'error');
                updateStatus('inactive', '扩展不可用');
            }
        }
        
        async function testPing() {
            try {
                addLog('开始Ping测试...', 'info');
                
                const api = chrome || browser;
                const startTime = Date.now();
                
                const response = await Promise.race([
                    api.runtime.sendMessage({ type: 'ping' }),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('Ping超时')), 5000)
                    )
                ]);
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                if (response && response.success) {
                    addLog(`Ping成功! 响应时间: ${duration}ms`, 'success');
                    addLog(`响应内容: ${JSON.stringify(response)}`, 'info');
                    updateStatus('active', 'Service Worker 正常运行');
                } else {
                    throw new Error('响应格式无效');
                }
                
            } catch (error) {
                addLog(`Ping失败: ${error.message}`, 'error');
                updateStatus('inactive', 'Service Worker 无响应');
            }
        }
        
        async function forceWakeUpServiceWorker() {
            try {
                addLog('执行强制唤醒序列...', 'info');
                
                const api = chrome || browser;
                
                // 方法1: 存储操作触发
                await api.storage.local.set({ _manualWakeUp: Date.now() });
                addLog('存储触发器已设置', 'info');
                
                // 方法2: 获取信息
                const platformInfo = await api.runtime.getPlatformInfo();
                addLog(`平台信息: ${JSON.stringify(platformInfo)}`, 'info');
                
                // 方法3: 等待后测试
                addLog('等待Service Worker启动...', 'info');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 测试是否唤醒成功
                const pingResponse = await Promise.race([
                    api.runtime.sendMessage({ type: 'ping' }),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('唤醒后Ping超时')), 3000)
                    )
                ]);
                
                if (pingResponse && pingResponse.success) {
                    addLog('强制唤醒成功!', 'success');
                    updateStatus('active', 'Service Worker 已唤醒');
                } else {
                    throw new Error('唤醒后Ping失败');
                }
                
            } catch (error) {
                addLog(`强制唤醒失败: ${error.message}`, 'error');
                updateStatus('inactive', '强制唤醒失败');
            }
        }
        
        // 事件监听器
        document.getElementById('checkStatus').addEventListener('click', checkExtensionStatus);
        document.getElementById('pingTest').addEventListener('click', testPing);
        document.getElementById('forceWakeUp').addEventListener('click', forceWakeUpServiceWorker);
        document.getElementById('clearLog').addEventListener('click', () => {
            log.innerHTML = '';
        });
        
        // 自动检查状态
        window.addEventListener('load', () => {
            addLog('Service Worker 测试工具已加载', 'success');
            checkExtensionStatus();
        });
        
        // 监听存储变化，检测Service Worker活动
        if (typeof chrome !== 'undefined' || typeof browser !== 'undefined') {
            const api = chrome || browser;
            api.storage.onChanged.addListener((changes, areaName) => {
                if (areaName === 'local') {
                    for (const [key, change] of Object.entries(changes)) {
                        if (key.includes('heartbeat') || key.includes('WakeUp')) {
                            addLog(`检测到存储变化: ${key}`, 'info');
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>