{"permissions": {"allow": ["Bash(npm install @mswjs/interceptors jsondiffpatch)", "Bash(npm run compile)", "Bash(npm run build)", "Bash(grep -n \"ToolManager\" /Users/<USER>/Documents/zyb/extension/fwyy-tools/entrypoints/popup/main.ts)", "Bash(grep -n \"^  }\" /Users/<USER>/Documents/zyb/extension/fwyy-tools/entrypoints/popup/main.ts)", "Bash(awk '/^class ToolManager/,/^}/ {print NR \"\"\"\": \"\"\"\" $0}' /Users/<USER>/Documents/zyb/extension/fwyy-tools/entrypoints/popup/main.ts)"], "deny": []}}