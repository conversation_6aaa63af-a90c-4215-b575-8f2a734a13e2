# Service Worker 保活优化方案

## 问题描述

在 Edge 浏览器中，扩展的 Service Worker 显示为"不活动"状态，导致 API 迁移验证工具在尝试 ping Service Worker 时出现超时错误。

## 根本原因分析

1. **Service Worker 生命周期**：Chrome/Edge 的 Service Worker 在空闲 30 秒后会自动挂起以节省资源
2. **代码问题**：原代码中调用了未定义的函数 `forceKeepServiceWorkerAlive()` 和 `forceWakeUpServiceWorker()`
3. **保活机制失效**：虽然定义了保活函数，但没有正确启动
4. **超时设置不足**：原始的 ping 超时时间（3-5秒）对于冷启动的 Service Worker 来说太短

## 优化方案

### 1. 多重保活机制 (`startMultipleKeepAliveMechanisms`)

#### 机制1：Chrome API 定期调用
```typescript
const apiInterval = setInterval(() => {
  Promise.all([
    browser.runtime.getPlatformInfo(),
    browser.storage.local.set({ _heartbeat: Date.now() }),
    browser.tabs.query({ active: true }).catch(() => [])
  ]).catch(() => {
    // 忽略错误，继续保活
  });
}, 15000); // 每15秒
```

#### 机制2：Alarms API 保活
```typescript
browser.alarms.create('serviceWorkerKeepAlive', { 
  delayInMinutes: 0.25, 
  periodInMinutes: 0.25 
});
```

#### 机制3：存储操作保活
```typescript
const storageInterval = setInterval(() => {
  browser.storage.local.set({ 
    _storageHeartbeat: Date.now(),
    _keepAliveCounter: Math.floor(Date.now() / 1000)
  });
}, 20000); // 每20秒
```

#### 机制4：端口连接保活
- 监听连接请求
- 自动重连机制
- 消息传递保活

### 2. 增强的强制唤醒机制

#### 在 background.ts 中：
- 立即激活序列
- 多轮 API 调用
- 连续轻量级操作

#### 在 api-migration-validator.ts 中：
- 四轮渐进式唤醒操作
- 更多的 API 调用组合
- 连续存储操作

### 3. 超时和重试优化

#### 原始设置：
- 重试次数：5次
- 超时时间：3-5秒
- 等待时间：1.5-3秒

#### 优化后设置：
- 重试次数：15次
- 超时时间：8-15秒（渐进式）
- 等待时间：2-6秒（渐进式）

```typescript
const timeout = i < 3 ? 15000 : (i < 8 ? 12000 : 8000);
const waitTime = i < 5 ? (2000 + (i * 1000)) : 6000;
```

## 实施的具体改动

### 1. entrypoints/background.ts
- ✅ 修复了未定义的函数调用
- ✅ 实现了 `startMultipleKeepAliveMechanisms()` 函数
- ✅ 添加了 `setupPortKeepAlive()` 函数
- ✅ 修复了类型错误（tabId 可能为 undefined）
- ✅ 修复了 `browser.runtime.getContexts()` 调用错误

### 2. tools/api-migration-validator.ts
- ✅ 增加了重试次数（5次 → 15次）
- ✅ 延长了超时时间（3-5秒 → 8-15秒）
- ✅ 优化了等待策略（渐进式增加）
- ✅ 增强了 `forceWakeUpServiceWorker()` 函数

### 3. 新增测试工具
- ✅ 创建了 `test-service-worker.html` 测试页面
- ✅ 提供实时状态监控
- ✅ 支持连续测试和统计分析

## 保活策略的工作原理

### 1. 定时器保活
- 每15秒调用 Chrome API
- 每20秒进行存储操作
- 每15秒（0.25分钟）触发 Alarm

### 2. 事件驱动保活
- 监听存储变化事件
- 监听端口连接事件
- 监听标签页事件

### 3. 冷启动优化
- 多轮渐进式唤醒
- 组合多种 API 调用
- 连续轻量级操作

## 使用说明

### 1. 重新加载扩展
在 `edge://extensions/` 页面重新加载扩展以应用更改。

### 2. 验证保活效果
1. 打开 `test-service-worker.html` 页面
2. 点击"测试 Service Worker"按钮
3. 观察状态变化和响应时间
4. 使用"开始连续测试"监控长期稳定性

### 3. 检查 Service Worker 状态
在 `edge://extensions/` 中查看扩展详情，Service Worker 应该显示为"活跃"状态。

## 预期效果

1. **Service Worker 持续活跃**：通过多重保活机制，Service Worker 应该能够保持长时间活跃
2. **减少 ping 超时**：增加的超时时间和重试次数应该显著减少超时错误
3. **更快的响应**：预热机制应该减少冷启动时间
4. **更好的稳定性**：多重保活策略提供冗余保护

## 注意事项

1. **性能影响**：保活机制会消耗一定的系统资源
2. **浏览器限制**：未来浏览器版本可能对保活行为有更严格的限制
3. **企业环境**：在企业/教育环境中，Chrome 官方允许持续保活
4. **监控建议**：建议定期检查保活效果，必要时调整策略

## 故障排除

如果 Service Worker 仍然出现问题：

1. **手动激活**：在扩展管理页面点击 Service Worker 链接
2. **重新加载扩展**：完全重新加载扩展
3. **检查控制台**：查看 Service Worker 控制台的错误信息
4. **使用测试页面**：通过测试页面监控实时状态
